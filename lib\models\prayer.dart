class Prayer {
  final String name;
  final String arabicName;
  final DateTime time;
  final bool isCompleted;
  final String id;

  Prayer({
    required this.name,
    required this.arabicName,
    required this.time,
    this.isCompleted = false,
    required this.id,
  });

  Prayer copyWith({
    String? name,
    String? arabicName,
    DateTime? time,
    bool? isCompleted,
    String? id,
  }) {
    return Prayer(
      name: name ?? this.name,
      arabicName: arabicName ?? this.arabicName,
      time: time ?? this.time,
      isCompleted: isCompleted ?? this.isCompleted,
      id: id ?? this.id,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'arabicName': arabicName,
      'time': time.toIso8601String(),
      'isCompleted': isCompleted,
      'id': id,
    };
  }

  factory Prayer.fromJson(Map<String, dynamic> json) {
    return Prayer(
      name: json['name'],
      arabicName: json['arabicName'],
      time: DateTime.parse(json['time']),
      isCompleted: json['isCompleted'] ?? false,
      id: json['id'],
    );
  }

  @override
  String toString() {
    return 'Prayer(name: $name, arabicName: $arabicName, time: $time, isCompleted: $isCompleted, id: $id)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Prayer &&
        other.name == name &&
        other.arabicName == arabicName &&
        other.time == time &&
        other.isCompleted == isCompleted &&
        other.id == id;
  }

  @override
  int get hashCode {
    return name.hashCode ^
        arabicName.hashCode ^
        time.hashCode ^
        isCompleted.hashCode ^
        id.hashCode;
  }
}

// قائمة الصلوات الخمس
class PrayerNames {
  static const String fajr = 'fajr';
  static const String dhuhr = 'dhuhr';
  static const String asr = 'asr';
  static const String maghrib = 'maghrib';
  static const String isha = 'isha';

  static const Map<String, String> arabicNames = {
    fajr: 'الفجر',
    dhuhr: 'الظهر',
    asr: 'العصر',
    maghrib: 'المغرب',
    isha: 'العشاء',
  };

  static const List<String> allPrayers = [
    fajr,
    dhuhr,
    asr,
    maghrib,
    isha,
  ];
}
