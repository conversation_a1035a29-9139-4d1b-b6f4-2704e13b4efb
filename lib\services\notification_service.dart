import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:timezone/timezone.dart' as tz;
import 'package:timezone/data/latest.dart' as tz;
import '../models/index.dart';

class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  final FlutterLocalNotificationsPlugin _flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();

  bool _isInitialized = false;

  /// تهيئة خدمة الإشعارات
  Future<void> initialize() async {
    if (_isInitialized) return;

    // تهيئة المناطق الزمنية
    tz.initializeTimeZones();

    // إعدادات Android
    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher');

    // إعدادات iOS
    const DarwinInitializationSettings initializationSettingsIOS =
        DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    const InitializationSettings initializationSettings =
        InitializationSettings(
      android: initializationSettingsAndroid,
      iOS: initializationSettingsIOS,
    );

    await _flutterLocalNotificationsPlugin.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );

    _isInitialized = true;
  }

  /// طلب الأذونات المطلوبة
  Future<bool> requestPermissions() async {
    if (await Permission.notification.isGranted) {
      return true;
    }

    final status = await Permission.notification.request();
    return status == PermissionStatus.granted;
  }

  /// معالج النقر على الإشعار
  void _onNotificationTapped(NotificationResponse notificationResponse) {
    // يمكن إضافة منطق للتنقل إلى صفحة معينة عند النقر على الإشعار
    print('تم النقر على الإشعار: ${notificationResponse.payload}');
  }

  /// جدولة إشعار لصلاة معينة
  Future<void> schedulePrayerNotification(Prayer prayer) async {
    if (!_isInitialized) await initialize();

    final now = DateTime.now();
    if (prayer.time.isBefore(now)) {
      return; // لا يمكن جدولة إشعار لوقت مضى
    }

    const AndroidNotificationDetails androidPlatformChannelSpecifics =
        AndroidNotificationDetails(
      'prayer_notifications',
      'إشعارات الصلاة',
      channelDescription: 'إشعارات أوقات الصلاة',
      importance: Importance.high,
      priority: Priority.high,
      playSound: true,
      enableVibration: true,
      icon: '@mipmap/ic_launcher',
    );

    const DarwinNotificationDetails iOSPlatformChannelSpecifics =
        DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    const NotificationDetails platformChannelSpecifics = NotificationDetails(
      android: androidPlatformChannelSpecifics,
      iOS: iOSPlatformChannelSpecifics,
    );

    await _flutterLocalNotificationsPlugin.zonedSchedule(
      prayer.id.hashCode,
      'حان وقت ${prayer.arabicName}',
      'حان الآن وقت صلاة ${prayer.arabicName}',
      tz.TZDateTime.from(prayer.time, tz.local),
      platformChannelSpecifics,
      androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
      uiLocalNotificationDateInterpretation:
          UILocalNotificationDateInterpretation.absoluteTime,
      payload: prayer.id,
    );
  }

  /// جدولة إشعارات لجميع الصلوات
  Future<void> scheduleAllPrayerNotifications(PrayerTimes prayerTimes) async {
    for (final prayer in prayerTimes.prayers) {
      await schedulePrayerNotification(prayer);
    }
  }

  /// إلغاء إشعار صلاة معينة
  Future<void> cancelPrayerNotification(String prayerId) async {
    await _flutterLocalNotificationsPlugin.cancel(prayerId.hashCode);
  }

  /// إلغاء جميع الإشعارات
  Future<void> cancelAllNotifications() async {
    await _flutterLocalNotificationsPlugin.cancelAll();
  }

  /// جدولة إشعار تذكير قبل الصلاة
  Future<void> scheduleReminderNotification(
    Prayer prayer,
    int minutesBefore,
  ) async {
    if (!_isInitialized) await initialize();

    final reminderTime = prayer.time.subtract(Duration(minutes: minutesBefore));
    final now = DateTime.now();

    if (reminderTime.isBefore(now)) {
      return; // لا يمكن جدولة تذكير لوقت مضى
    }

    const AndroidNotificationDetails androidPlatformChannelSpecifics =
        AndroidNotificationDetails(
      'prayer_reminders',
      'تذكيرات الصلاة',
      channelDescription: 'تذكيرات قبل أوقات الصلاة',
      importance: Importance.default,
      priority: Priority.default,
      icon: '@mipmap/ic_launcher',
    );

    const DarwinNotificationDetails iOSPlatformChannelSpecifics =
        DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    const NotificationDetails platformChannelSpecifics = NotificationDetails(
      android: androidPlatformChannelSpecifics,
      iOS: iOSPlatformChannelSpecifics,
    );

    await _flutterLocalNotificationsPlugin.zonedSchedule(
      '${prayer.id}_reminder'.hashCode,
      'تذكير: ${prayer.arabicName}',
      'سيحين وقت صلاة ${prayer.arabicName} خلال $minutesBefore دقيقة',
      tz.TZDateTime.from(reminderTime, tz.local),
      platformChannelSpecifics,
      androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
      uiLocalNotificationDateInterpretation:
          UILocalNotificationDateInterpretation.absoluteTime,
      payload: '${prayer.id}_reminder',
    );
  }

  /// الحصول على الإشعارات المجدولة
  Future<List<PendingNotificationRequest>> getPendingNotifications() async {
    return await _flutterLocalNotificationsPlugin.pendingNotificationRequests();
  }

  /// إظهار إشعار فوري
  Future<void> showInstantNotification({
    required String title,
    required String body,
    String? payload,
  }) async {
    if (!_isInitialized) await initialize();

    const AndroidNotificationDetails androidPlatformChannelSpecifics =
        AndroidNotificationDetails(
      'instant_notifications',
      'الإشعارات الفورية',
      channelDescription: 'إشعارات فورية من التطبيق',
      importance: Importance.default,
      priority: Priority.default,
      icon: '@mipmap/ic_launcher',
    );

    const DarwinNotificationDetails iOSPlatformChannelSpecifics =
        DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    const NotificationDetails platformChannelSpecifics = NotificationDetails(
      android: androidPlatformChannelSpecifics,
      iOS: iOSPlatformChannelSpecifics,
    );

    await _flutterLocalNotificationsPlugin.show(
      DateTime.now().millisecondsSinceEpoch.remainder(100000),
      title,
      body,
      platformChannelSpecifics,
      payload: payload,
    );
  }
}
