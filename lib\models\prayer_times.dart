import 'prayer.dart';

class PrayerTimes {
  final DateTime date;
  final List<Prayer> prayers;
  final String timezone;
  final double latitude;
  final double longitude;

  PrayerTimes({
    required this.date,
    required this.prayers,
    required this.timezone,
    required this.latitude,
    required this.longitude,
  });

  PrayerTimes copyWith({
    DateTime? date,
    List<Prayer>? prayers,
    String? timezone,
    double? latitude,
    double? longitude,
  }) {
    return PrayerTimes(
      date: date ?? this.date,
      prayers: prayers ?? this.prayers,
      timezone: timezone ?? this.timezone,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'date': date.toIso8601String(),
      'prayers': prayers.map((prayer) => prayer.toJson()).toList(),
      'timezone': timezone,
      'latitude': latitude,
      'longitude': longitude,
    };
  }

  factory PrayerTimes.fromJson(Map<String, dynamic> json) {
    return PrayerTimes(
      date: DateTime.parse(json['date']),
      prayers: (json['prayers'] as List)
          .map((prayerJson) => Prayer.fromJson(prayerJson))
          .toList(),
      timezone: json['timezone'],
      latitude: json['latitude'],
      longitude: json['longitude'],
    );
  }

  // الحصول على صلاة معينة بالاسم
  Prayer? getPrayerByName(String name) {
    try {
      return prayers.firstWhere((prayer) => prayer.name == name);
    } catch (e) {
      return null;
    }
  }

  // الحصول على الصلاة التالية
  Prayer? getNextPrayer() {
    final now = DateTime.now();
    for (final prayer in prayers) {
      if (prayer.time.isAfter(now)) {
        return prayer;
      }
    }
    return null; // لا توجد صلاة متبقية لليوم
  }

  // الحصول على الصلاة الحالية
  Prayer? getCurrentPrayer() {
    final now = DateTime.now();
    Prayer? currentPrayer;
    
    for (final prayer in prayers) {
      if (prayer.time.isBefore(now) || prayer.time.isAtSameMomentAs(now)) {
        currentPrayer = prayer;
      } else {
        break;
      }
    }
    
    return currentPrayer;
  }

  // الحصول على الصلوات غير المكتملة
  List<Prayer> getIncompletePrayers() {
    return prayers.where((prayer) => !prayer.isCompleted).toList();
  }

  // الحصول على الصلوات المكتملة
  List<Prayer> getCompletedPrayers() {
    return prayers.where((prayer) => prayer.isCompleted).toList();
  }

  // تحديث حالة صلاة معينة
  PrayerTimes updatePrayerStatus(String prayerId, bool isCompleted) {
    final updatedPrayers = prayers.map((prayer) {
      if (prayer.id == prayerId) {
        return prayer.copyWith(isCompleted: isCompleted);
      }
      return prayer;
    }).toList();

    return copyWith(prayers: updatedPrayers);
  }

  @override
  String toString() {
    return 'PrayerTimes(date: $date, prayers: $prayers, timezone: $timezone, latitude: $latitude, longitude: $longitude)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PrayerTimes &&
        other.date == date &&
        other.prayers.length == prayers.length &&
        other.timezone == timezone &&
        other.latitude == latitude &&
        other.longitude == longitude;
  }

  @override
  int get hashCode {
    return date.hashCode ^
        prayers.hashCode ^
        timezone.hashCode ^
        latitude.hashCode ^
        longitude.hashCode;
  }
}
