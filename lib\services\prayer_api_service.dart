import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:intl/intl.dart';
import '../models/index.dart';

class PrayerApiService {
  static const String _baseUrl = 'http://api.aladhan.com/v1';
  
  // طريقة حساب الصلاة للمذهب الشيعي
  // Method 0: <PERSON>-<PERSON>ari, Leva Institute, Qum
  static const int _shiaCalculationMethod = 0;

  /// الحصول على أوقات الصلاة لتاريخ معين
  Future<PrayerTimes?> getPrayerTimes({
    required double latitude,
    required double longitude,
    required String timezone,
    DateTime? date,
  }) async {
    try {
      final targetDate = date ?? DateTime.now();
      final dateString = DateFormat('dd-MM-yyyy').format(targetDate);
      
      final uri = Uri.parse('$_baseUrl/timings/$dateString').replace(
        queryParameters: {
          'latitude': latitude.toString(),
          'longitude': longitude.toString(),
          'method': _shiaCalculationMethod.toString(),
          'timezone': timezone,
        },
      );

      final response = await http.get(uri);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        
        if (data['code'] == 200 && data['status'] == 'OK') {
          return _parsePrayerTimes(
            data['data'],
            targetDate,
            latitude,
            longitude,
            timezone,
          );
        }
      }
      
      return null;
    } catch (e) {
      print('خطأ في الحصول على أوقات الصلاة: $e');
      return null;
    }
  }

  /// الحصول على أوقات الصلاة لشهر كامل
  Future<List<PrayerTimes>?> getMonthlyPrayerTimes({
    required double latitude,
    required double longitude,
    required String timezone,
    DateTime? month,
  }) async {
    try {
      final targetMonth = month ?? DateTime.now();
      final monthString = DateFormat('MM-yyyy').format(targetMonth);
      
      final uri = Uri.parse('$_baseUrl/calendar/$monthString').replace(
        queryParameters: {
          'latitude': latitude.toString(),
          'longitude': longitude.toString(),
          'method': _shiaCalculationMethod.toString(),
          'timezone': timezone,
        },
      );

      final response = await http.get(uri);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        
        if (data['code'] == 200 && data['status'] == 'OK') {
          final List<dynamic> monthData = data['data'];
          final List<PrayerTimes> monthlyTimes = [];
          
          for (final dayData in monthData) {
            final dateString = dayData['date']['gregorian']['date'];
            final date = DateFormat('dd-MM-yyyy').parse(dateString);
            
            final prayerTimes = _parsePrayerTimes(
              dayData,
              date,
              latitude,
              longitude,
              timezone,
            );
            
            if (prayerTimes != null) {
              monthlyTimes.add(prayerTimes);
            }
          }
          
          return monthlyTimes;
        }
      }
      
      return null;
    } catch (e) {
      print('خطأ في الحصول على أوقات الصلاة الشهرية: $e');
      return null;
    }
  }

  /// تحليل البيانات المستلمة من API وتحويلها إلى PrayerTimes
  PrayerTimes? _parsePrayerTimes(
    Map<String, dynamic> data,
    DateTime date,
    double latitude,
    double longitude,
    String timezone,
  ) {
    try {
      final timings = data['timings'] as Map<String, dynamic>;
      final prayers = <Prayer>[];

      // إنشاء قائمة الصلوات مع أوقاتها
      for (final prayerName in PrayerNames.allPrayers) {
        final timeString = _getTimeFromTimings(timings, prayerName);
        if (timeString != null) {
          final prayerTime = _parseTime(timeString, date);
          if (prayerTime != null) {
            prayers.add(Prayer(
              id: '${prayerName}_${DateFormat('yyyy-MM-dd').format(date)}',
              name: prayerName,
              arabicName: PrayerNames.arabicNames[prayerName] ?? prayerName,
              time: prayerTime,
              isCompleted: false,
            ));
          }
        }
      }

      // ترتيب الصلوات حسب الوقت
      prayers.sort((a, b) => a.time.compareTo(b.time));

      return PrayerTimes(
        date: date,
        prayers: prayers,
        timezone: timezone,
        latitude: latitude,
        longitude: longitude,
      );
    } catch (e) {
      print('خطأ في تحليل أوقات الصلاة: $e');
      return null;
    }
  }

  /// الحصول على وقت الصلاة من البيانات المستلمة
  String? _getTimeFromTimings(Map<String, dynamic> timings, String prayerName) {
    switch (prayerName) {
      case PrayerNames.fajr:
        return timings['Fajr'];
      case PrayerNames.dhuhr:
        return timings['Dhuhr'];
      case PrayerNames.asr:
        return timings['Asr'];
      case PrayerNames.maghrib:
        return timings['Maghrib'];
      case PrayerNames.isha:
        return timings['Isha'];
      default:
        return null;
    }
  }

  /// تحويل النص الزمني إلى DateTime
  DateTime? _parseTime(String timeString, DateTime date) {
    try {
      // إزالة المنطقة الزمنية من النص إذا كانت موجودة
      final cleanTimeString = timeString.split(' ')[0];
      final timeParts = cleanTimeString.split(':');
      
      if (timeParts.length >= 2) {
        final hour = int.parse(timeParts[0]);
        final minute = int.parse(timeParts[1]);
        
        return DateTime(
          date.year,
          date.month,
          date.day,
          hour,
          minute,
        );
      }
      
      return null;
    } catch (e) {
      print('خطأ في تحليل الوقت: $timeString - $e');
      return null;
    }
  }

  /// التحقق من اتصال الإنترنت
  Future<bool> checkInternetConnection() async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/status'),
        headers: {'Content-Type': 'application/json'},
      ).timeout(const Duration(seconds: 5));
      
      return response.statusCode == 200;
    } catch (e) {
      return false;
    }
  }
}
