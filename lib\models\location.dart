class LocationData {
  final double latitude;
  final double longitude;
  final String timezone;
  final String city;
  final String country;

  LocationData({
    required this.latitude,
    required this.longitude,
    required this.timezone,
    required this.city,
    required this.country,
  });

  LocationData copyWith({
    double? latitude,
    double? longitude,
    String? timezone,
    String? city,
    String? country,
  }) {
    return LocationData(
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      timezone: timezone ?? this.timezone,
      city: city ?? this.city,
      country: country ?? this.country,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'latitude': latitude,
      'longitude': longitude,
      'timezone': timezone,
      'city': city,
      'country': country,
    };
  }

  factory LocationData.fromJson(Map<String, dynamic> json) {
    return LocationData(
      latitude: json['latitude'],
      longitude: json['longitude'],
      timezone: json['timezone'],
      city: json['city'],
      country: json['country'],
    );
  }

  @override
  String toString() {
    return 'LocationData(latitude: $latitude, longitude: $longitude, timezone: $timezone, city: $city, country: $country)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is LocationData &&
        other.latitude == latitude &&
        other.longitude == longitude &&
        other.timezone == timezone &&
        other.city == city &&
        other.country == country;
  }

  @override
  int get hashCode {
    return latitude.hashCode ^
        longitude.hashCode ^
        timezone.hashCode ^
        city.hashCode ^
        country.hashCode;
  }
}

// مواقع مشهورة في العالم الإسلامي
class PredefinedLocations {
  static const List<LocationData> cities = [
    LocationData(
      latitude: 21.4225,
      longitude: 39.8262,
      timezone: 'Asia/Riyadh',
      city: 'مكة المكرمة',
      country: 'السعودية',
    ),
    LocationData(
      latitude: 24.4539,
      longitude: 39.6040,
      timezone: 'Asia/Riyadh',
      city: 'المدينة المنورة',
      country: 'السعودية',
    ),
    LocationData(
      latitude: 24.7136,
      longitude: 46.6753,
      timezone: 'Asia/Riyadh',
      city: 'الرياض',
      country: 'السعودية',
    ),
    LocationData(
      latitude: 33.3152,
      longitude: 44.3661,
      timezone: 'Asia/Baghdad',
      city: 'بغداد',
      country: 'العراق',
    ),
    LocationData(
      latitude: 32.0853,
      longitude: 44.4176,
      timezone: 'Asia/Baghdad',
      city: 'النجف',
      country: 'العراق',
    ),
    LocationData(
      latitude: 32.6160,
      longitude: 44.0251,
      timezone: 'Asia/Baghdad',
      city: 'كربلاء',
      country: 'العراق',
    ),
    LocationData(
      latitude: 35.6944,
      longitude: 51.4215,
      timezone: 'Asia/Tehran',
      city: 'طهران',
      country: 'إيران',
    ),
    LocationData(
      latitude: 36.2605,
      longitude: 59.6168,
      timezone: 'Asia/Tehran',
      city: 'مشهد',
      country: 'إيران',
    ),
    LocationData(
      latitude: 29.6107,
      longitude: 52.5313,
      timezone: 'Asia/Tehran',
      city: 'شيراز',
      country: 'إيران',
    ),
    LocationData(
      latitude: 30.0444,
      longitude: 31.2357,
      timezone: 'Africa/Cairo',
      city: 'القاهرة',
      country: 'مصر',
    ),
    LocationData(
      latitude: 33.5138,
      longitude: 36.2765,
      timezone: 'Asia/Damascus',
      city: 'دمشق',
      country: 'سوريا',
    ),
    LocationData(
      latitude: 33.8547,
      longitude: 35.8623,
      timezone: 'Asia/Beirut',
      city: 'بيروت',
      country: 'لبنان',
    ),
  ];

  static LocationData? findByCity(String cityName) {
    try {
      return cities.firstWhere((location) => location.city == cityName);
    } catch (e) {
      return null;
    }
  }

  static List<String> getCityNames() {
    return cities.map((location) => location.city).toList();
  }
}
